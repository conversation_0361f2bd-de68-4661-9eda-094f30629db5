{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "OcsUrl": "http://*************:8081/ocsapi/v1", "OcsUser": "ocsapi", "OcsPasswd": "qwert123", "Scalar": {"Title": "OCS Inventory .NET Server API", "Version": "v1.0.0", "Description": "A comprehensive .NET wrapper API for OCS Inventory Server, providing access to computer inventory, software management, SNMP devices, IP discovery, and CVE vulnerability information.", "Contact": {"Name": "OCS Inventory .NET Server", "Url": "https://github.com/your-repo/ocs-inventory-dotnet-server"}, "License": {"Name": "MIT", "Url": "https://opensource.org/licenses/MIT"}}}
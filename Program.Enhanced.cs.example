using OCSInventoryDotnetServer.Services;
using OCSInventoryDotnetServer.Extensions;
using OCSInventoryDotnetServer.Middleware;
using OCSInventoryDotnetServer.HealthChecks;
using Scalar.AspNetCore;
using Microsoft.AspNetCore.RateLimiting;
using System.Threading.RateLimiting;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddOpenApi();
builder.Services.AddScoped<OcsApiService>();

// 添加健康检查
builder.Services.AddHealthChecks()
    .AddCheck<OcsApiHealthCheck>("ocs-api");

// 添加内存缓存
builder.Services.AddMemoryCache();

// 添加限流
builder.Services.AddRateLimiter(options =>
{
    options.AddFixedWindowLimiter("api", opt =>
    {
        opt.Window = TimeSpan.FromMinutes(1);
        opt.PermitLimit = 100;
        opt.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
        opt.QueueLimit = 10;
    });
});

// 添加CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigins", policy =>
    {
        policy.WithOrigins("http://localhost:3000", "https://yourdomain.com")
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

// 添加响应压缩
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
});

var app = builder.Build();

// Configure the HTTP request pipeline.
app.UseErrorHandling();

// 使用响应压缩
app.UseResponseCompression();

// 使用限流
app.UseRateLimiter();

// 使用CORS
app.UseCors("AllowSpecificOrigins");

if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

// 添加健康检查端点
app.MapHealthChecks("/health");
app.MapHealthChecks("/health/ready", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
{
    Predicate = check => check.Tags.Contains("ready")
});

//app.UseHttpsRedirection();

// 配置API端点（应用限流）
app.MapGroup("/api")
   .RequireRateLimiting("api")
   .WithOpenApi()
   .MapComputerEndpoints()
   .MapSoftwareEndpoints()
   .MapSnmpEndpoints()
   .MapIpDiscoverEndpoints()
   .MapCveEndpoints();

// 为了向后兼容，保留根级别的端点
app.MapComputerEndpoints();
app.MapSoftwareEndpoints();
app.MapSnmpEndpoints();
app.MapIpDiscoverEndpoints();
app.MapCveEndpoints();

//// 添加静态文件支持
app.UseStaticFiles();

app.MapScalarApiReference();

app.Run();

// 扩展方法示例
public static class WebApplicationExtensions
{
    public static RouteGroupBuilder MapComputerEndpoints(this RouteGroupBuilder group)
    {
        // 这里可以添加特定于组的配置
        return group;
    }

    public static RouteGroupBuilder MapSoftwareEndpoints(this RouteGroupBuilder group)
    {
        return group;
    }

    public static RouteGroupBuilder MapSnmpEndpoints(this RouteGroupBuilder group)
    {
        return group;
    }

    public static RouteGroupBuilder MapIpDiscoverEndpoints(this RouteGroupBuilder group)
    {
        return group;
    }

    public static RouteGroupBuilder MapCveEndpoints(this RouteGroupBuilder group)
    {
        return group;
    }
}

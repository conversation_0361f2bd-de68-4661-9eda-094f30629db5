# OCS Inventory .NET Server

A comprehensive .NET wrapper API for OCS Inventory Server, providing access to computer inventory, software management, SNMP devices, IP discovery, and CVE vulnerability information.

## Features

- **Computer Management**: Get computer details, search computers, and retrieve inventory information
- **Software Management**: List and search installed software across your infrastructure
- **SNMP Device Support**: Manage and query SNMP-enabled devices
- **IP Discovery**: Network discovery and device enumeration
- **CVE Vulnerability Tracking**: Security vulnerability management and reporting
- **Comprehensive Error Handling**: Robust error handling with detailed error responses
- **Parameter Validation**: Input validation for all API endpoints
- **OpenAPI Documentation**: Interactive API documentation with Scalar UI

## API Endpoints

### Computer Endpoints
- `GET /devices` - Get all device IDs
- `GET /computers` - Get computer details with pagination
- `GET /computer/{id}` - Get specific computer details
- `GET /computers/search` - Search computers with filters
- `GET /computers/lastupdate/{timestamp}` - Get recently updated computers

### Software Endpoints
- `GET /softwares` - Get software list with optional filtering

### SNMP Device Endpoints
- `GET /snmps/typeList` - Get SNMP device types
- `GET /snmp/{tableTypeName}` - Get SNMP devices by type
- `GET /snmp/{tableTypeName}/{id}` - Get specific SNMP device

### IP Discovery Endpoints
- `GET /ipdiscover` - Get IP discovery networks
- `GET /ipdiscover/network/{netid}` - Get devices in specific network
- `GET /ipdiscover/tag/{tag}` - Get devices by tag

### CVE Endpoints
- `GET /cve/cvss` - Get CVEs sorted by CVSS score
- `GET /cve/software` - Get CVEs grouped by software
- `GET /cve/computer` - Get CVEs grouped by computer
- `GET /cve/computerslist` - Get vulnerable computers
- `GET /cve/history` - Get CVE history

## Configuration

Update `appsettings.json` with your OCS Inventory server details:

```json
{
  "OcsUrl": "http://your-ocs-server:8081/ocsapi/v1",
  "OcsUser": "your-api-username",
  "OcsPasswd": "your-api-password"
}
```

## Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd OCSInventoryDotnetServer
   ```

2. **Configure the application**
   - Update `appsettings.json` with your OCS Inventory server details

3. **Run the application**
   ```bash
   dotnet run
   ```

4. **Access the API documentation**
   - Open your browser and navigate to `http://localhost:5099/scalar/v1`
   - Interactive API documentation will be available

## Testing

Use the provided `OCSInventoryDotnetServer.http` file to test the API endpoints. This file contains sample requests for all available endpoints.

## Error Handling

The API includes comprehensive error handling:

- **400 Bad Request**: Invalid parameters or validation errors
- **404 Not Found**: Resource not found
- **408 Request Timeout**: Request timeout
- **500 Internal Server Error**: Server errors

Error responses include detailed information:
```json
{
  "statusCode": 400,
  "message": "Invalid argument",
  "details": "Start parameter must be non-negative",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Parameter Validation

All endpoints include parameter validation:
- **Pagination**: `start` must be non-negative, `limit` must be positive and ≤ 1000
- **IDs**: Must be positive integers
- **Network IDs**: Must be valid IP address format
- **Search operators**: Must be one of: `like`, `not like`, `=`, `!=`, `<`, `>`, `<=`, `>=`

## Architecture

The project follows a clean architecture pattern:

- **Services**: `OcsApiService` handles all OCS Inventory API communication
- **Extensions**: `ApiEndpointsExtensions` organizes API endpoints by category
- **Middleware**: `ErrorHandlingMiddleware` provides global error handling
- **Validation**: `ValidationExtensions` provides parameter validation
- **Entities**: Strongly-typed models for all API responses

## Dependencies

- **.NET 9.0**: Latest .NET framework
- **Flurl.Http**: HTTP client for API communication
- **Scalar.AspNetCore**: Interactive API documentation
- **Microsoft.AspNetCore.OpenApi**: OpenAPI support

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the API documentation at `/scalar/v1`
2. Review the test cases in `OCSInventoryDotnetServer.http`
3. Create an issue in the repository

## Roadmap

- [ ] Add POST/PUT/DELETE endpoints for data modification
- [ ] Add authentication and authorization
- [ ] Add caching for improved performance
- [ ] Add unit and integration tests
- [ ] Add Docker support
- [ ] Add health checks
- [ ] Add metrics and monitoring

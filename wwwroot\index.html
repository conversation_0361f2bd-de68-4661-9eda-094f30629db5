<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCS Inventory .NET Server</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid #667eea;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 0.5rem;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .endpoint {
            background: #e9ecef;
            padding: 0.5rem;
            border-radius: 4px;
            font-family: monospace;
            margin: 0.25rem 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🖥️ OCS Inventory .NET Server</h1>
        <p>A comprehensive .NET wrapper API for OCS Inventory Server</p>
    </div>

    <div class="card">
        <h2>📚 API Documentation</h2>
        <p>Explore the interactive API documentation with Scalar UI:</p>
        <a href="/scalar/v1" class="btn">View API Documentation</a>
    </div>

    <div class="card">
        <h2>🔍 Health Check</h2>
        <p>Check the health status of the API:</p>
        <a href="/health" class="btn">Health Status</a>
    </div>

    <div class="card">
        <h2>🚀 Quick Start</h2>
        <p>Here are some example API endpoints you can try:</p>
        
        <h3>Computer Management</h3>
        <div class="endpoint">GET /devices</div>
        <div class="endpoint">GET /computers?start=0&limit=10</div>
        <div class="endpoint">GET /computer/{id}</div>
        
        <h3>Software Management</h3>
        <div class="endpoint">GET /softwares?start=0&limit=10</div>
        
        <h3>SNMP Devices</h3>
        <div class="endpoint">GET /snmps/typeList</div>
        
        <h3>IP Discovery</h3>
        <div class="endpoint">GET /ipdiscover</div>
        
        <h3>CVE Information</h3>
        <div class="endpoint">GET /cve/cvss?start=0&limit=10</div>
    </div>

    <div class="card">
        <h2>⚙️ Configuration</h2>
        <p>Make sure to configure your OCS Inventory server connection in <code>appsettings.json</code>:</p>
        <pre style="background: #2d3748; color: #e2e8f0; padding: 1rem; border-radius: 4px; overflow-x: auto;">
{
  "OcsUrl": "http://your-ocs-server:8081/ocsapi/v1",
  "OcsUser": "your-api-username",
  "OcsPasswd": "your-api-password"
}</pre>
    </div>

    <div class="card">
        <h2>📖 Features</h2>
        <ul>
            <li>✅ Complete OCS Inventory REST API wrapper</li>
            <li>✅ Comprehensive error handling and validation</li>
            <li>✅ Interactive API documentation</li>
            <li>✅ Health checks and monitoring</li>
            <li>✅ Structured logging and error responses</li>
        </ul>
    </div>

    <footer style="text-align: center; margin-top: 2rem; padding: 1rem; color: #666;">
        <p>OCS Inventory .NET Server - Built with ❤️ using .NET 9.0</p>
    </footer>
</body>
</html>

using OCSInventoryDotnetServer.Services;
using Microsoft.AspNetCore.Http;

namespace OCSInventoryDotnetServer.Extensions
{
    /// <summary>
    /// API端点扩展方法
    /// </summary>
    public static class ApiEndpointsExtensions
    {
        /// <summary>
        /// 配置计算机相关的API端点
        /// </summary>
        public static void MapComputerEndpoints(this WebApplication app)
        {
            // 获取所有计算机ID
            app.MapGet("/devices", async (OcsApiService ocsService) =>
            {
                try
                {
                    var devices = await ocsService.GetComputerIdsAsync();
                    return Results.Ok(devices.Select(x => x.Id));
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving devices: {ex.Message}");
                }
            })
            .WithDescription("获取设备id")
            .WithOpenApi(operation => {
                operation.Summary = "获取所有设备ID";
                operation.Description = "获取所有计算机设备的ID列表";
                return operation;
            });

            // 获取计算机详情列表
            app.MapGet("/computers", async (int start, int limit, OcsApiService ocsService) =>
            {
                // 验证分页参数
                var validationResult = ValidationExtensions.ValidatePaginationParameters(start, limit);
                if (validationResult != null)
                {
                    return validationResult;
                }

                try
                {
                    var computers = await ocsService.GetComputersAsync(start, limit);
                    return Results.Ok(computers);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving computers: {ex.Message}");
                }
            })
            .WithDescription("获取指定范围内的计算机详细信息")
            .WithOpenApi(operation => {
                operation.Summary = "获取指定范围内的计算机详细信息";
                operation.Description = "获取指定范围内的计算机详细信息，包括硬件、软件、网络等完整信息";
                return operation;
            });

            // 获取单个计算机详情
            app.MapGet("/computer/{id}", async (int id, string? specificSection, string? where, string? @operator, string? value, OcsApiService ocsService) =>
            {
                // 验证ID参数
                var idValidation = ValidationExtensions.ValidateIdParameter(id);
                if (idValidation != null)
                {
                    return idValidation;
                }

                // 验证搜索操作符
                var operatorValidation = ValidationExtensions.ValidateSearchOperator(@operator);
                if (operatorValidation != null)
                {
                    return operatorValidation;
                }

                // 如果指定了操作符，必须同时指定where和value
                if (!string.IsNullOrEmpty(@operator) && (string.IsNullOrEmpty(where) || string.IsNullOrEmpty(value)))
                {
                    return Results.BadRequest(new { error = "When operator is specified, both 'where' and 'value' parameters are required" });
                }

                try
                {
                    var computer = await ocsService.GetComputerAsync(id, specificSection, where, @operator, value);
                    return Results.Ok(computer);
                }
                catch (Flurl.Http.FlurlHttpException ex)
                {
                    if (ex.StatusCode == 404)
                    {
                        return Results.NotFound("Raptor not found");
                    }
                    return Results.Problem($"Error retrieving computer details: {ex.Message}");
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Internal server error: {ex.Message}");
                }
            })
            .WithDescription("根据id获取计算机详细信息")
            .WithOpenApi(operation => {
                operation.Summary = "根据id获取计算机详细信息";
                operation.Description = "根据id获取计算机详细信息，包括硬件、软件、网络等完整信息。可以指定特定部分和搜索条件。";
                return operation;
            });

            // 获取最近更新的计算机
            app.MapGet("/computers/lastupdate/{timestamp?}", async (long? timestamp, OcsApiService ocsService) =>
            {
                try
                {
                    var computers = await ocsService.GetLastUpdatedComputersAsync(timestamp);
                    return Results.Ok(computers);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving last updated computers: {ex.Message}");
                }
            })
            .WithDescription("列出最后更新的计算机id")
            .WithOpenApi(operation => {
                operation.Summary = "列出最后更新的计算机id";
                operation.Description = "列出最后更新的计算机id，timestamp参数为天数，默认为1天(86400秒)";
                return operation;
            });

            // 搜索计算机
            app.MapGet("/computers/search", async (int start, int limit, string? userid, string? name, string? ipaddr, string? orderby, OcsApiService ocsService) =>
            {
                try
                {
                    var computers = await ocsService.SearchComputersAsync(start, limit, userid, name, ipaddr, orderby);
                    return Results.Ok(computers);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error searching computers: {ex.Message}");
                }
            })
            .WithDescription("搜索计算机")
            .WithOpenApi(operation => {
                operation.Summary = "搜索计算机";
                operation.Description = "根据指定条件搜索计算机，支持按用户ID、名称、IP地址等条件搜索，并可指定排序方式";
                return operation;
            });
        }

        /// <summary>
        /// 配置软件相关的API端点
        /// </summary>
        public static void MapSoftwareEndpoints(this WebApplication app)
        {
            app.MapGet("/softwares", async (int start, int limit, string? soft, OcsApiService ocsService) =>
            {
                try
                {
                    var softwares = await ocsService.GetSoftwaresAsync(start, limit, soft);
                    return Results.Ok(softwares);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving software list: {ex.Message}");
                }
            })
            .WithDescription("获取软件列表")
            .WithOpenApi(operation => {
                operation.Summary = "获取软件列表";
                operation.Description = "获取软件列表，包括名称、发布者和版本信息，可按软件名称过滤";
                return operation;
            });
        }

        /// <summary>
        /// 配置SNMP设备相关的API端点
        /// </summary>
        public static void MapSnmpEndpoints(this WebApplication app)
        {
            app.MapGet("/snmps/typeList", async (OcsApiService ocsService) =>
            {
                try
                {
                    var types = await ocsService.GetSnmpTypesAsync();
                    return Results.Ok(types);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving SNMP types: {ex.Message}");
                }
            })
            .WithDescription("获取SNMP设备类型列表")
            .WithOpenApi(operation => {
                operation.Summary = "获取SNMP设备类型列表";
                operation.Description = "获取所有SNMP设备类型的列表";
                return operation;
            });

            app.MapGet("/snmp/{tableTypeName}", async (string tableTypeName, int start, int limit, OcsApiService ocsService) =>
            {
                try
                {
                    var devices = await ocsService.GetSnmpDevicesAsync(tableTypeName, start, limit);
                    return Results.Ok(devices);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving SNMP devices: {ex.Message}");
                }
            })
            .WithDescription("获取指定类型的SNMP设备列表")
            .WithOpenApi(operation => {
                operation.Summary = "获取指定类型的SNMP设备列表";
                operation.Description = "根据表类型名称获取SNMP设备详情列表";
                return operation;
            });

            app.MapGet("/snmp/{tableTypeName}/{id}", async (string tableTypeName, int id, OcsApiService ocsService) =>
            {
                try
                {
                    var device = await ocsService.GetSnmpDeviceAsync(tableTypeName, id);
                    return Results.Ok(device);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving SNMP device: {ex.Message}");
                }
            })
            .WithDescription("获取指定SNMP设备详情")
            .WithOpenApi(operation => {
                operation.Summary = "获取指定SNMP设备详情";
                operation.Description = "根据表类型名称和设备ID获取SNMP设备详情";
                return operation;
            });
        }

        /// <summary>
        /// 配置IP发现相关的API端点
        /// </summary>
        public static void MapIpDiscoverEndpoints(this WebApplication app)
        {
            app.MapGet("/ipdiscover", async (int? start, int? limit, OcsApiService ocsService) =>
            {
                try
                {
                    var networks = await ocsService.GetIpDiscoverNetworksAsync(start, limit);
                    return Results.Ok(networks);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving IP discover networks: {ex.Message}");
                }
            })
            .WithDescription("获取IP发现网络列表")
            .WithOpenApi(operation => {
                operation.Summary = "获取IP发现网络列表";
                operation.Description = "获取所有IP发现网络的列表";
                return operation;
            });

            app.MapGet("/ipdiscover/network/{netid}", async (string netid, OcsApiService ocsService) =>
            {
                try
                {
                    var devices = await ocsService.GetNetworkDevicesAsync(netid);
                    return Results.Ok(devices);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving network devices: {ex.Message}");
                }
            })
            .WithDescription("获取指定网络的设备列表")
            .WithOpenApi(operation => {
                operation.Summary = "获取指定网络的设备列表";
                operation.Description = "根据网络ID获取该网络下的所有设备";
                return operation;
            });

            app.MapGet("/ipdiscover/tag/{tag}", async (string tag, OcsApiService ocsService) =>
            {
                try
                {
                    var devices = await ocsService.GetDevicesByTagAsync(tag);
                    return Results.Ok(devices);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving devices by tag: {ex.Message}");
                }
            })
            .WithDescription("根据标签获取设备列表")
            .WithOpenApi(operation => {
                operation.Summary = "根据标签获取设备列表";
                operation.Description = "根据指定标签获取所有相关设备";
                return operation;
            });
        }

        /// <summary>
        /// 配置CVE相关的API端点
        /// </summary>
        public static void MapCveEndpoints(this WebApplication app)
        {
            app.MapGet("/cve/cvss", async (int? start, int? limit, OcsApiService ocsService) =>
            {
                try
                {
                    var cves = await ocsService.GetCveByCvssAsync(start, limit);
                    return Results.Ok(cves);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving CVE by CVSS: {ex.Message}");
                }
            })
            .WithDescription("按CVSS评分获取CVE列表")
            .WithOpenApi(operation => {
                operation.Summary = "按CVSS评分获取CVE列表";
                operation.Description = "获取按CVSS评分排序的CVE漏洞列表";
                return operation;
            });

            app.MapGet("/cve/software", async (int? start, int? limit, OcsApiService ocsService) =>
            {
                try
                {
                    var cves = await ocsService.GetCveBySoftwareAsync(start, limit);
                    return Results.Ok(cves);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving CVE by software: {ex.Message}");
                }
            })
            .WithDescription("按软件获取CVE列表")
            .WithOpenApi(operation => {
                operation.Summary = "按软件获取CVE列表";
                operation.Description = "获取按软件分类的CVE漏洞列表";
                return operation;
            });

            app.MapGet("/cve/computer", async (int? start, int? limit, OcsApiService ocsService) =>
            {
                try
                {
                    var cves = await ocsService.GetCveByComputerAsync(start, limit);
                    return Results.Ok(cves);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving CVE by computer: {ex.Message}");
                }
            })
            .WithDescription("按计算机获取CVE列表")
            .WithOpenApi(operation => {
                operation.Summary = "按计算机获取CVE列表";
                operation.Description = "获取按计算机分类的CVE漏洞列表";
                return operation;
            });

            app.MapGet("/cve/computerslist", async (OcsApiService ocsService) =>
            {
                try
                {
                    var computers = await ocsService.GetVulnerableComputersAsync();
                    return Results.Ok(computers);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving vulnerable computers: {ex.Message}");
                }
            })
            .WithDescription("获取易受攻击的计算机列表")
            .WithOpenApi(operation => {
                operation.Summary = "获取易受攻击的计算机列表";
                operation.Description = "获取至少存在一个CVE漏洞的计算机列表";
                return operation;
            });

            app.MapGet("/cve/history", async (OcsApiService ocsService) =>
            {
                try
                {
                    var history = await ocsService.GetCveHistoryAsync();
                    return Results.Ok(history);
                }
                catch (Exception ex)
                {
                    return Results.Problem($"Error retrieving CVE history: {ex.Message}");
                }
            })
            .WithDescription("获取CVE历史记录")
            .WithOpenApi(operation => {
                operation.Summary = "获取CVE历史记录";
                operation.Description = "获取CVE漏洞的历史记录信息";
                return operation;
            });
        }
    }
}

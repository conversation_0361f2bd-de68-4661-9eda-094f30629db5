using System.Net;
using System.Text.Json;
using Flurl.Http;

namespace OCSInventoryDotnetServer.Middleware
{
    /// <summary>
    /// 全局错误处理中间件
    /// </summary>
    public class ErrorHandlingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ErrorHandlingMiddleware> _logger;

        public ErrorHandlingMiddleware(RequestDelegate next, ILogger<ErrorHandlingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred");
                await HandleExceptionAsync(context, ex);
            }
        }

        private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";

            var response = new ErrorResponse();

            switch (exception)
            {
                case FlurlHttpException flurlEx:
                    response.StatusCode = flurlEx.StatusCode ?? 500;
                    response.Message = flurlEx.StatusCode switch
                    {
                        404 => "Resource not found",
                        401 => "Unauthorized access",
                        403 => "Forbidden access",
                        400 => "Bad request",
                        _ => "External API error"
                    };
                    response.Details = flurlEx.Message;
                    break;

                case ArgumentException argEx:
                    response.StatusCode = 400;
                    response.Message = "Invalid argument";
                    response.Details = argEx.Message;
                    break;

                case ArgumentNullException nullEx:
                    response.StatusCode = 400;
                    response.Message = "Missing required parameter";
                    response.Details = nullEx.ParamName;
                    break;

                case TimeoutException:
                    response.StatusCode = 408;
                    response.Message = "Request timeout";
                    response.Details = "The request took too long to complete";
                    break;

                default:
                    response.StatusCode = 500;
                    response.Message = "Internal server error";
                    response.Details = "An unexpected error occurred";
                    break;
            }

            context.Response.StatusCode = response.StatusCode;

            var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await context.Response.WriteAsync(jsonResponse);
        }
    }

    /// <summary>
    /// 错误响应模型
    /// </summary>
    public class ErrorResponse
    {
        /// <summary>
        /// HTTP状态码
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 错误详情
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 错误处理中间件扩展方法
    /// </summary>
    public static class ErrorHandlingMiddlewareExtensions
    {
        /// <summary>
        /// 使用错误处理中间件
        /// </summary>
        public static IApplicationBuilder UseErrorHandling(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<ErrorHandlingMiddleware>();
        }
    }
}

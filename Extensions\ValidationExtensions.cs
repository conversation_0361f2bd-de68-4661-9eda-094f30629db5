using Microsoft.AspNetCore.Http;

namespace OCSInventoryDotnetServer.Extensions
{
    /// <summary>
    /// 参数验证扩展方法
    /// </summary>
    public static class ValidationExtensions
    {
        /// <summary>
        /// 验证分页参数
        /// </summary>
        /// <param name="start">起始位置</param>
        /// <param name="limit">限制数量</param>
        /// <returns>验证结果</returns>
        public static IResult? ValidatePaginationParameters(int start, int limit)
        {
            if (start < 0)
            {
                return Results.BadRequest(new { error = "Start parameter must be non-negative", parameter = "start", value = start });
            }

            if (limit <= 0)
            {
                return Results.BadRequest(new { error = "Limit parameter must be positive", parameter = "limit", value = limit });
            }

            if (limit > 1000)
            {
                return Results.BadRequest(new { error = "Limit parameter cannot exceed 1000", parameter = "limit", value = limit });
            }

            return null;
        }

        /// <summary>
        /// 验证ID参数
        /// </summary>
        /// <param name="id">ID值</param>
        /// <param name="parameterName">参数名称</param>
        /// <returns>验证结果</returns>
        public static IResult? ValidateIdParameter(int id, string parameterName = "id")
        {
            if (id <= 0)
            {
                return Results.BadRequest(new { error = $"{parameterName} parameter must be positive", parameter = parameterName, value = id });
            }

            return null;
        }

        /// <summary>
        /// 验证字符串参数
        /// </summary>
        /// <param name="value">字符串值</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="required">是否必需</param>
        /// <param name="maxLength">最大长度</param>
        /// <returns>验证结果</returns>
        public static IResult? ValidateStringParameter(string? value, string parameterName, bool required = false, int maxLength = 255)
        {
            if (required && string.IsNullOrWhiteSpace(value))
            {
                return Results.BadRequest(new { error = $"{parameterName} parameter is required", parameter = parameterName });
            }

            if (!string.IsNullOrEmpty(value) && value.Length > maxLength)
            {
                return Results.BadRequest(new { error = $"{parameterName} parameter cannot exceed {maxLength} characters", parameter = parameterName, value = value.Length });
            }

            return null;
        }

        /// <summary>
        /// 验证时间戳参数
        /// </summary>
        /// <param name="timestamp">时间戳值</param>
        /// <returns>验证结果</returns>
        public static IResult? ValidateTimestampParameter(long? timestamp)
        {
            if (timestamp.HasValue && timestamp.Value < 0)
            {
                return Results.BadRequest(new { error = "Timestamp parameter must be non-negative", parameter = "timestamp", value = timestamp.Value });
            }

            // 验证时间戳不能太大（比如不能超过10年）
            if (timestamp.HasValue && timestamp.Value > 315360000) // 10年的秒数
            {
                return Results.BadRequest(new { error = "Timestamp parameter is too large", parameter = "timestamp", value = timestamp.Value });
            }

            return null;
        }

        /// <summary>
        /// 验证搜索操作符
        /// </summary>
        /// <param name="operator">操作符</param>
        /// <returns>验证结果</returns>
        public static IResult? ValidateSearchOperator(string? @operator)
        {
            if (string.IsNullOrEmpty(@operator))
            {
                return null;
            }

            var validOperators = new[] { "like", "not like", "=", "!=", "<", ">", "<=", ">=" };
            if (!validOperators.Contains(@operator, StringComparer.OrdinalIgnoreCase))
            {
                return Results.BadRequest(new 
                { 
                    error = "Invalid operator", 
                    parameter = "operator", 
                    value = @operator,
                    validValues = validOperators 
                });
            }

            return null;
        }

        /// <summary>
        /// 验证网络ID格式
        /// </summary>
        /// <param name="netid">网络ID</param>
        /// <returns>验证结果</returns>
        public static IResult? ValidateNetworkId(string netid)
        {
            if (string.IsNullOrWhiteSpace(netid))
            {
                return Results.BadRequest(new { error = "Network ID is required", parameter = "netid" });
            }

            // 简单的IP地址格式验证
            var parts = netid.Split('.');
            if (parts.Length != 4)
            {
                return Results.BadRequest(new { error = "Invalid network ID format", parameter = "netid", value = netid });
            }

            foreach (var part in parts)
            {
                if (!int.TryParse(part, out var num) || num < 0 || num > 255)
                {
                    return Results.BadRequest(new { error = "Invalid network ID format", parameter = "netid", value = netid });
                }
            }

            return null;
        }

        /// <summary>
        /// 验证标签参数
        /// </summary>
        /// <param name="tag">标签值</param>
        /// <returns>验证结果</returns>
        public static IResult? ValidateTagParameter(string tag)
        {
            if (string.IsNullOrWhiteSpace(tag))
            {
                return Results.BadRequest(new { error = "Tag parameter is required", parameter = "tag" });
            }

            if (tag.Length > 50)
            {
                return Results.BadRequest(new { error = "Tag parameter cannot exceed 50 characters", parameter = "tag", value = tag.Length });
            }

            return null;
        }

        /// <summary>
        /// 验证表类型名称
        /// </summary>
        /// <param name="tableTypeName">表类型名称</param>
        /// <returns>验证结果</returns>
        public static IResult? ValidateTableTypeName(string tableTypeName)
        {
            if (string.IsNullOrWhiteSpace(tableTypeName))
            {
                return Results.BadRequest(new { error = "Table type name is required", parameter = "tableTypeName" });
            }

            // 验证表类型名称格式（只允许字母、数字和下划线）
            if (!System.Text.RegularExpressions.Regex.IsMatch(tableTypeName, @"^[a-zA-Z0-9_]+$"))
            {
                return Results.BadRequest(new { error = "Invalid table type name format", parameter = "tableTypeName", value = tableTypeName });
            }

            if (tableTypeName.Length > 100)
            {
                return Results.BadRequest(new { error = "Table type name cannot exceed 100 characters", parameter = "tableTypeName", value = tableTypeName.Length });
            }

            return null;
        }
    }
}

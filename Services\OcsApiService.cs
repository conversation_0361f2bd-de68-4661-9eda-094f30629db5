using Flurl.Http;
using OCSInventoryDotnetServer.Entity;

namespace OCSInventoryDotnetServer.Services
{
    /// <summary>
    /// OCS Inventory API服务类
    /// </summary>
    public class OcsApiService
    {
        private readonly string _baseUrl;
        private readonly string _username;
        private readonly string _password;

        public OcsApiService(IConfiguration configuration)
        {
            _baseUrl = configuration.GetValue<string>("OcsUrl") ?? throw new ArgumentNullException("OcsUrl");
            _username = configuration.GetValue<string>("OcsUser") ?? throw new ArgumentNullException("OcsUser");
            _password = configuration.GetValue<string>("OcsPasswd") ?? throw new ArgumentNullException("OcsPasswd");
        }

        /// <summary>
        /// 获取所有计算机ID列表
        /// </summary>
        public async Task<List<DeviceId>> GetComputerIdsAsync()
        {
            var url = $"{_baseUrl}/computers/listID";
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<DeviceId>>();
        }

        /// <summary>
        /// 获取计算机详情列表
        /// </summary>
        public async Task<ComputerDetailsResponse> GetComputersAsync(int start, int limit)
        {
            var url = $"{_baseUrl}/computers?start={start}&limit={limit}";
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<ComputerDetailsResponse>();
        }

        /// <summary>
        /// 获取单个计算机详情
        /// </summary>
        public async Task<ComputerDetailsResponse> GetComputerAsync(int id, string? specificSection = null, string? where = null, string? @operator = null, string? value = null)
        {
            var url = $"{_baseUrl}/computer/{id}";
            
            if (!string.IsNullOrEmpty(specificSection))
            {
                url += $"/{specificSection}";
            }
            
            var queryParams = new List<string>();
            if (!string.IsNullOrEmpty(where))
                queryParams.Add($"where={Uri.EscapeDataString(where)}");
            if (!string.IsNullOrEmpty(@operator))
                queryParams.Add($"operator={Uri.EscapeDataString(@operator)}");
            if (!string.IsNullOrEmpty(value))
                queryParams.Add($"value={Uri.EscapeDataString(value)}");
            
            if (queryParams.Count > 0)
            {
                url += "?" + string.Join("&", queryParams);
            }
            
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<ComputerDetailsResponse>();
        }

        /// <summary>
        /// 获取最近更新的计算机
        /// </summary>
        public async Task<List<DeviceId>> GetLastUpdatedComputersAsync(long? timestamp = null)
        {
            var timestampValue = timestamp ?? 86400;
            var url = $"{_baseUrl}/computers/lastupdate/{timestampValue}";
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<DeviceId>>();
        }

        /// <summary>
        /// 搜索计算机
        /// </summary>
        public async Task<List<DeviceId>> SearchComputersAsync(int start, int limit, string? userid = null, string? name = null, string? ipaddr = null, string? orderby = null)
        {
            var url = $"{_baseUrl}/computers/search?start={start}&limit={limit}";
            
            if (!string.IsNullOrEmpty(userid))
                url += $"&userid={Uri.EscapeDataString(userid)}";
            if (!string.IsNullOrEmpty(name))
                url += $"&name={Uri.EscapeDataString(name)}";
            if (!string.IsNullOrEmpty(ipaddr))
                url += $"&ipaddr={Uri.EscapeDataString(ipaddr)}";
            if (!string.IsNullOrEmpty(orderby))
                url += $"&orderby={Uri.EscapeDataString(orderby)}";
            
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<DeviceId>>();
        }

        /// <summary>
        /// 获取软件列表
        /// </summary>
        public async Task<List<SoftwareInfo>> GetSoftwaresAsync(int start, int limit, string? soft = null)
        {
            var url = $"{_baseUrl}/softwares?start={start}&limit={limit}";
            
            if (!string.IsNullOrEmpty(soft))
            {
                url += $"&soft={Uri.EscapeDataString(soft)}";
            }
            
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<SoftwareInfo>>();
        }

        /// <summary>
        /// 获取SNMP设备类型列表
        /// </summary>
        public async Task<List<SnmpType>> GetSnmpTypesAsync()
        {
            var url = $"{_baseUrl}/snmps/typeList";
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<SnmpType>>();
        }

        /// <summary>
        /// 获取指定类型的SNMP设备列表
        /// </summary>
        public async Task<List<object>> GetSnmpDevicesAsync(string tableTypeName, int start, int limit)
        {
            var url = $"{_baseUrl}/snmp/{tableTypeName}?start={start}&limit={limit}";
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<object>>();
        }

        /// <summary>
        /// 获取指定SNMP设备详情
        /// </summary>
        public async Task<List<object>> GetSnmpDeviceAsync(string tableTypeName, int id)
        {
            var url = $"{_baseUrl}/snmp/{tableTypeName}/{id}";
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<object>>();
        }

        /// <summary>
        /// 获取IP发现网络列表
        /// </summary>
        public async Task<List<IpDiscoverNetwork>> GetIpDiscoverNetworksAsync(int? start = null, int? limit = null)
        {
            var url = $"{_baseUrl}/ipdiscover";
            
            var queryParams = new List<string>();
            if (start.HasValue)
                queryParams.Add($"start={start.Value}");
            if (limit.HasValue)
                queryParams.Add($"limit={limit.Value}");
            
            if (queryParams.Count > 0)
            {
                url += "?" + string.Join("&", queryParams);
            }
            
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<IpDiscoverNetwork>>();
        }

        /// <summary>
        /// 获取指定网络的设备列表
        /// </summary>
        public async Task<List<IpDiscoverDevice>> GetNetworkDevicesAsync(string netid)
        {
            var url = $"{_baseUrl}/ipdiscover/network/{netid}";
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<IpDiscoverDevice>>();
        }

        /// <summary>
        /// 根据标签获取设备列表
        /// </summary>
        public async Task<List<IpDiscoverDevice>> GetDevicesByTagAsync(string tag)
        {
            var url = $"{_baseUrl}/ipdiscover/tag/{tag}";
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<IpDiscoverDevice>>();
        }

        /// <summary>
        /// 按CVSS评分获取CVE列表
        /// </summary>
        public async Task<List<CveInfo>> GetCveByCvssAsync(int? start = null, int? limit = null)
        {
            var url = $"{_baseUrl}/cve/cvss";
            
            var queryParams = new List<string>();
            if (start.HasValue)
                queryParams.Add($"start={start.Value}");
            if (limit.HasValue)
                queryParams.Add($"limit={limit.Value}");
            
            if (queryParams.Count > 0)
            {
                url += "?" + string.Join("&", queryParams);
            }
            
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<CveInfo>>();
        }

        /// <summary>
        /// 按软件获取CVE列表
        /// </summary>
        public async Task<List<CveBySoftware>> GetCveBySoftwareAsync(int? start = null, int? limit = null)
        {
            var url = $"{_baseUrl}/cve/software";
            
            var queryParams = new List<string>();
            if (start.HasValue)
                queryParams.Add($"start={start.Value}");
            if (limit.HasValue)
                queryParams.Add($"limit={limit.Value}");
            
            if (queryParams.Count > 0)
            {
                url += "?" + string.Join("&", queryParams);
            }
            
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<CveBySoftware>>();
        }

        /// <summary>
        /// 按计算机获取CVE列表
        /// </summary>
        public async Task<List<CveByComputer>> GetCveByComputerAsync(int? start = null, int? limit = null)
        {
            var url = $"{_baseUrl}/cve/computer";
            
            var queryParams = new List<string>();
            if (start.HasValue)
                queryParams.Add($"start={start.Value}");
            if (limit.HasValue)
                queryParams.Add($"limit={limit.Value}");
            
            if (queryParams.Count > 0)
            {
                url += "?" + string.Join("&", queryParams);
            }
            
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<CveByComputer>>();
        }

        /// <summary>
        /// 获取易受攻击的计算机列表
        /// </summary>
        public async Task<List<VulnerableComputer>> GetVulnerableComputersAsync()
        {
            var url = $"{_baseUrl}/cve/computerslist";
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<VulnerableComputer>>();
        }

        /// <summary>
        /// 获取CVE历史记录
        /// </summary>
        public async Task<List<CveHistory>> GetCveHistoryAsync()
        {
            var url = $"{_baseUrl}/cve/history";
            return await url
                .WithBasicAuth(_username, _password)
                .GetJsonAsync<List<CveHistory>>();
        }
    }
}

﻿using Flurl.Http;
using Microsoft.AspNetCore.Http;
using OCSInventoryDotnetServer.Entity;
using Scalar.AspNetCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();


var app = builder.Build();
var baseUrl = app.Configuration.GetValue<string>("OcsUrl");
var ocsUser = app.Configuration.GetValue<string>("OcsUser");
var ocsPwd = app.Configuration.GetValue<string>("OcsPasswd");
// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

//app.UseHttpsRedirection();



app.MapGet("/devices", async () =>
{
    var url= $"{baseUrl}/computers/listID";
    var resp = await url
     .WithBasicAuth(ocsUser, ocsPwd)
     .GetJsonAsync<List<DeviceId>>();
     
    return resp.Select(x=>x.Id);
})
.WithDescription("获取设备id'")
.WithOpenApi(operation => {
    operation.Summary = "获取所有设备ID";

    return operation;
});


app.MapGet("/ListComputersDetails", async (int start,int limit) =>
{
    var url = $"{baseUrl}/computers?start={start}&limit={limit}";
    var resp = await url
     .WithBasicAuth(ocsUser, ocsPwd)
     .GetJsonAsync<List<DeviceId>>();

    return resp.Select(x => x.Id);
})
.WithDescription("获取设备明细列表'")
.WithOpenApi(operation => {
    operation.Summary = "获取设备明细";

    return operation;
});

//// 添加静态文件支持
app.UseStaticFiles();


app.MapScalarApiReference();

app.Run();



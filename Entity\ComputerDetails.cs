namespace OCSInventoryDotnetServer.Entity
{
    /// <summary>
    /// 计算机详情响应类，包含多台计算机的详情信息
    /// </summary>
    public class ComputerDetailsResponse : Dictionary<string, ComputerDetails>
    {
    }

    /// <summary>
    /// 计算机详情信息，包含硬件、软件等各类详细信息
    /// </summary>
    public class ComputerDetails
    {
        /// <summary>
        /// 账户信息列表
        /// </summary>
        public List<AccountInfo> AccountInfo { get; set; } = new();
        /// <summary>
        /// 电池信息列表
        /// </summary>
        public List<Battery> Batteries { get; set; } = new();
        /// <summary>
        /// BIOS信息列表
        /// </summary>
        public List<Bios> Bios { get; set; } = new();
        /// <summary>
        /// 控制器列表
        /// </summary>
        public List<Controller> Controllers { get; set; } = new();
        /// <summary>
        /// CPU信息列表
        /// </summary>
        public List<Cpu> Cpus { get; set; } = new();
        /// <summary>
        /// 设备信息列表
        /// </summary>
        public List<Device> Devices { get; set; } = new();
        /// <summary>
        /// 下载历史记录列表
        /// </summary>
        public List<DownloadHistory> Download_History { get; set; } = new();
        /// <summary>
        /// 驱动器信息列表
        /// </summary>
        public List<Drive> Drives { get; set; } = new();
        /// <summary>
        /// 组缓存列表
        /// </summary>
        public List<GroupsCache> Groups_Cache { get; set; } = new();
        /// <summary>
        /// 硬件信息
        /// </summary>
        public Hardware Hardware { get; set; } = new();
        /// <summary>
        /// 输入设备列表
        /// </summary>
        public List<Input> Inputs { get; set; } = new();
        /// <summary>
        /// IT管理评论列表
        /// </summary>
        public List<ItMgmtComment> Itmgmt_Comments { get; set; } = new();
        /// <summary>
        /// Java信息列表
        /// </summary>
        public List<JavaInfo> Javainfo { get; set; } = new();
        /// <summary>
        /// 日志列表
        /// </summary>
        public List<JournalLog> Journallog { get; set; } = new();
        /// <summary>
        /// 本地组列表
        /// </summary>
        public List<LocalGroup> Local_Groups { get; set; } = new();
        /// <summary>
        /// 本地用户列表
        /// </summary>
        public List<LocalUser> Local_Users { get; set; } = new();
        /// <summary>
        /// 内存信息列表
        /// </summary>
        public List<Memory> Memories { get; set; } = new();
        /// <summary>
        /// 调制解调器列表
        /// </summary>
        public List<Modem> Modems { get; set; } = new();
        /// <summary>
        /// 显示器列表
        /// </summary>
        public List<Monitor> Monitors { get; set; } = new();
        /// <summary>
        /// 网络信息列表
        /// </summary>
        public List<Network> Networks { get; set; } = new();
        /// <summary>
        /// 端口列表
        /// </summary>
        public List<Port> Ports { get; set; } = new();
        /// <summary>
        /// 打印机列表
        /// </summary>
        public List<Printer> Printers { get; set; } = new();
        /// <summary>
        /// 注册表列表
        /// </summary>
        public List<Registry> Registry { get; set; } = new();
        /// <summary>
        /// 存储库列表
        /// </summary>
        public List<Repository> Repository { get; set; } = new();
        /// <summary>
        /// SIM卡列表
        /// </summary>
        public List<Sim> Sim { get; set; } = new();
        /// <summary>
        /// 插槽列表
        /// </summary>
        public List<Slot> Slots { get; set; } = new();
        /// <summary>
        /// 软件列表
        /// </summary>
        public List<Software> Software { get; set; } = new();
        /// <summary>
        /// 声音设备列表
        /// </summary>
        public List<Sound> Sounds { get; set; } = new();
        /// <summary>
        /// 存储设备列表
        /// </summary>
        public List<Storage> Storages { get; set; } = new();
        /// <summary>
        /// USB设备列表
        /// </summary>
        public List<UsbDevice> Usbdevices { get; set; } = new();
        /// <summary>
        /// 视频设备列表
        /// </summary>
        public List<Video> Videos { get; set; } = new();
        /// <summary>
        /// 虚拟机列表
        /// </summary>
        public List<VirtualMachine> Virtualmachines { get; set; } = new();
    }

    /// <summary>
    /// 账户信息类
    /// </summary>
    public class AccountInfo
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 标签
        /// </summary>
        public string TAG { get; set; } = "";
    }

    /// <summary>
    /// 电池信息类
    /// </summary>
    public class Battery
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 电池ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 电池名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// 电池化学类型
        /// </summary>
        public string CHEMISTRY { get; set; } = "";
        /// <summary>
        /// 电压
        /// </summary>
        public string VOLTAGE { get; set; } = "";
        /// <summary>
        /// 容量
        /// </summary>
        public string CAPACITY { get; set; } = "";
    }

    /// <summary>
    /// BIOS信息类
    /// </summary>
    public class Bios
    {
        /// <summary>
        /// 资产标签
        /// </summary>
        public string ASSETTAG { get; set; } = "";
        /// <summary>
        /// BIOS日期
        /// </summary>
        public string BDATE { get; set; } = "";
        /// <summary>
        /// BIOS制造商
        /// </summary>
        public string BMANUFACTURER { get; set; } = "";
        /// <summary>
        /// BIOS版本
        /// </summary>
        public string BVERSION { get; set; } = "";
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 主板制造商
        /// </summary>
        public string MMANUFACTURER { get; set; } = "";
        /// <summary>
        /// 主板型号
        /// </summary>
        public string MMODEL { get; set; } = "";
        /// <summary>
        /// 主板序列号
        /// </summary>
        public string MSN { get; set; } = "";
        /// <summary>
        /// 系统制造商
        /// </summary>
        public string SMANUFACTURER { get; set; } = "";
        /// <summary>
        /// 系统型号
        /// </summary>
        public string SMODEL { get; set; } = "";
        /// <summary>
        /// 系统序列号
        /// </summary>
        public string SSN { get; set; } = "";
        /// <summary>
        /// 类型
        /// </summary>
        public string TYPE { get; set; } = "";
    }

    /// <summary>
    /// 控制器信息类
    /// </summary>
    public class Controller
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string CAPTION { get; set; } = "";
        /// <summary>
        /// 描述
        /// </summary>
        public string DESCRIPTION { get; set; } = "";
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 控制器ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 制造商
        /// </summary>
        public string MANUFACTURER { get; set; } = "";
        /// <summary>
        /// 名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// 类型
        /// </summary>
        public string TYPE { get; set; } = "";
        /// <summary>
        /// 版本
        /// </summary>
        public string VERSION { get; set; } = "";
    }

    /// <summary>
    /// CPU信息类
    /// </summary>
    public class Cpu
    {
        /// <summary>
        /// 核心数
        /// </summary>
        public int CORES { get; set; }
        /// <summary>
        /// CPU架构
        /// </summary>
        public string CPUARCH { get; set; } = "";
        /// <summary>
        /// 当前地址宽度
        /// </summary>
        public int CURRENT_ADDRESS_WIDTH { get; set; }
        /// <summary>
        /// 当前速度
        /// </summary>
        public string CURRENT_SPEED { get; set; } = "";
        /// <summary>
        /// 数据宽度
        /// </summary>
        public int DATA_WIDTH { get; set; }
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// CPU ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// L2缓存大小
        /// </summary>
        public string L2CACHESIZE { get; set; } = "";
        /// <summary>
        /// 逻辑CPU数
        /// </summary>
        public int LOGICAL_CPUS { get; set; }
        /// <summary>
        /// 制造商
        /// </summary>
        public string MANUFACTURER { get; set; } = "";
        /// <summary>
        /// 序列号
        /// </summary>
        public string SERIALNUMBER { get; set; } = "";
        /// <summary>
        /// 插槽类型
        /// </summary>
        public string SOCKET { get; set; } = "";
        /// <summary>
        /// 速度
        /// </summary>
        public string SPEED { get; set; } = "";
        /// <summary>
        /// 类型
        /// </summary>
        public string TYPE { get; set; } = "";
        /// <summary>
        /// 电压
        /// </summary>
        public string VOLTAGE { get; set; } = "";
    }

    /// <summary>
    /// 设备信息类
    /// </summary>
    public class Device
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// 设备类型
        /// </summary>
        public string TYPE { get; set; } = "";
    }

    /// <summary>
    /// 下载历史记录类
    /// </summary>
    public class DownloadHistory
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 下载记录ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 包ID
        /// </summary>
        public string PKG_ID { get; set; } = "";
        /// <summary>
        /// 包名称
        /// </summary>
        public string PKG_NAME { get; set; } = "";
    }

    /// <summary>
    /// 驱动器信息类
    /// </summary>
    public class Drive
    {
        /// <summary>
        /// 创建日期
        /// </summary>
        public string CREATEDATE { get; set; } = "";
        /// <summary>
        /// 文件系统类型
        /// </summary>
        public string FILESYSTEM { get; set; } = "";
        /// <summary>
        /// 可用空间
        /// </summary>
        public int FREE { get; set; }
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 驱动器ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 驱动器盘符
        /// </summary>
        public string LETTER { get; set; } = "";
        /// <summary>
        /// 文件数量
        /// </summary>
        public int NUMFILES { get; set; }
        /// <summary>
        /// 总空间
        /// </summary>
        public int TOTAL { get; set; }
        /// <summary>
        /// 驱动器类型
        /// </summary>
        public string TYPE { get; set; } = "";
        /// <summary>
        /// 卷标
        /// </summary>
        public string VOLUMN { get; set; } = "";
    }

    /// <summary>
    /// 组缓存信息类
    /// </summary>
    public class GroupsCache
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 组缓存ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 缓存ID
        /// </summary>
        public string CACHE_ID { get; set; } = "";
    }

    /// <summary>
    /// 硬件信息类
    /// </summary>
    public class Hardware
    {
        /// <summary>
        /// 系统架构
        /// </summary>
        public string ARCH { get; set; } = "";
        /// <summary>
        /// 归档状态
        /// </summary>
        public string? ARCHIVE { get; set; }
        /// <summary>
        /// 类别ID
        /// </summary>
        public string? CATEGORY_ID { get; set; }
        /// <summary>
        /// 校验和
        /// </summary>
        public int CHECKSUM { get; set; }
        /// <summary>
        /// 默认网关
        /// </summary>
        public string DEFAULTGATEWAY { get; set; } = "";
        /// <summary>
        /// 描述信息
        /// </summary>
        public string? DESCRIPTION { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DEVICEID { get; set; } = "";
        /// <summary>
        /// DNS服务器
        /// </summary>
        public string? DNS { get; set; }
        /// <summary>
        /// 最后连接时间
        /// </summary>
        public string? ETIME { get; set; }
        /// <summary>
        /// 保真度
        /// </summary>
        public int FIDELITY { get; set; }
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// IP地址
        /// </summary>
        public string IPADDR { get; set; } = "";
        /// <summary>
        /// IP来源
        /// </summary>
        public string IPSRC { get; set; } = "";
        /// <summary>
        /// 最后连接时间
        /// </summary>
        public string LASTCOME { get; set; } = "";
        /// <summary>
        /// 最后日期
        /// </summary>
        public string LASTDATE { get; set; } = "";
        /// <summary>
        /// 内存大小(MB)
        /// </summary>
        public int MEMORY { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// 操作系统备注
        /// </summary>
        public string OSCOMMENTS { get; set; } = "";
        /// <summary>
        /// 操作系统名称
        /// </summary>
        public string OSNAME { get; set; } = "";
        /// <summary>
        /// 操作系统版本
        /// </summary>
        public string OSVERSION { get; set; } = "";
        /// <summary>
        /// 处理器数量
        /// </summary>
        public int PROCESSORN { get; set; }
        /// <summary>
        /// 处理器速度
        /// </summary>
        public int PROCESSORS { get; set; }
        /// <summary>
        /// 处理器类型
        /// </summary>
        public string PROCESSORT { get; set; } = "";
        /// <summary>
        /// 质量状态
        /// </summary>
        public string? QUALITY { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int SSTATE { get; set; }
        /// <summary>
        /// 交换空间大小(MB)
        /// </summary>
        public int SWAP { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public int TYPE { get; set; }
        /// <summary>
        /// 用户代理
        /// </summary>
        public string USERAGENT { get; set; } = "";
        /// <summary>
        /// 用户域
        /// </summary>
        public string USERDOMAIN { get; set; } = "";
        /// <summary>
        /// 用户ID
        /// </summary>
        public string USERID { get; set; } = "";
        /// <summary>
        /// 唯一标识符
        /// </summary>
        public string UUID { get; set; } = "";
        /// <summary>
        /// Windows公司信息
        /// </summary>
        public string? WINCOMPANY { get; set; }
        /// <summary>
        /// Windows所有者
        /// </summary>
        public string WINOWNER { get; set; } = "";
        /// <summary>
        /// Windows产品ID
        /// </summary>
        public string WINPRODID { get; set; } = "";
        /// <summary>
        /// Windows产品密钥
        /// </summary>
        public string WINPRODKEY { get; set; } = "";
        /// <summary>
        /// 工作组
        /// </summary>
        public string WORKGROUP { get; set; } = "";
    }

    /// <summary>
    /// 输入设备信息类
    /// </summary>
    public class Input
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string CAPTION { get; set; } = "";
        /// <summary>
        /// 描述
        /// </summary>
        public string DESCRIPTION { get; set; } = "";
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 输入设备ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 接口类型
        /// </summary>
        public string INTERFACE { get; set; } = "";
        /// <summary>
        /// 制造商
        /// </summary>
        public string MANUFACTURER { get; set; } = "";
        /// <summary>
        /// 点类型
        /// </summary>
        public string POINTTYPE { get; set; } = "";
        /// <summary>
        /// 类型
        /// </summary>
        public string TYPE { get; set; } = "";
    }

    /// <summary>
    /// IT管理评论信息类
    /// </summary>
    public class ItMgmtComment
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 评论ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 评论内容
        /// </summary>
        public string COMMENTS { get; set; } = "";
    }

    /// <summary>
    /// Java信息类
    /// </summary>
    public class JavaInfo
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// Java信息ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// Java名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// Java版本
        /// </summary>
        public string VERSION { get; set; } = "";
    }

    /// <summary>
    /// 日志信息类
    /// </summary>
    public class JournalLog
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 日志ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 日志名称
        /// </summary>
        public string LOGNAME { get; set; } = "";
    }

    /// <summary>
    /// 本地组信息类
    /// </summary>
    public class LocalGroup
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 本地组ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 组名称
        /// </summary>
        public string NAME { get; set; } = "";
    }

    /// <summary>
    /// 本地用户信息类
    /// </summary>
    public class LocalUser
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 本地用户ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 用户名
        /// </summary>
        public string NAME { get; set; } = "";
    }

    /// <summary>
    /// 内存信息类
    /// </summary>
    public class Memory
    {
        /// <summary>
        /// 内存容量(MB)
        /// </summary>
        public int CAPACITY { get; set; }
        /// <summary>
        /// 标题
        /// </summary>
        public string CAPTION { get; set; } = "";
        /// <summary>
        /// 描述
        /// </summary>
        public string DESCRIPTION { get; set; } = "";
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 内存ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 插槽数量
        /// </summary>
        public int NUMSLOTS { get; set; }
        /// <summary>
        /// 用途
        /// </summary>
        public string PURPOSE { get; set; } = "";
        /// <summary>
        /// 序列号
        /// </summary>
        public string SERIALNUMBER { get; set; } = "";
        /// <summary>
        /// 速度
        /// </summary>
        public string SPEED { get; set; } = "";
        /// <summary>
        /// 内存类型
        /// </summary>
        public string TYPE { get; set; } = "";
    }

    /// <summary>
    /// 调制解调器信息类
    /// </summary>
    public class Modem
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 调制解调器ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 调制解调器名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// 调制解调器类型
        /// </summary>
        public string TYPE { get; set; } = "";
    }

    /// <summary>
    /// 显示器信息类
    /// </summary>
    public class Monitor
    {
        /// <summary>
        /// 显示器标题
        /// </summary>
        public string CAPTION { get; set; } = "";
        /// <summary>
        /// 显示器描述
        /// </summary>
        public string DESCRIPTION { get; set; } = "";
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 显示器ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 显示器制造商
        /// </summary>
        public string MANUFACTURER { get; set; } = "";
        /// <summary>
        /// 显示器序列号
        /// </summary>
        public string SERIAL { get; set; } = "";
        /// <summary>
        /// 显示器类型
        /// </summary>
        public string TYPE { get; set; } = "";
    }

    /// <summary>
    /// 网络信息类
    /// </summary>
    public class Network
    {
        /// <summary>
        /// 网络描述
        /// </summary>
        public string DESCRIPTION { get; set; } = "";
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 网络ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// IP地址
        /// </summary>
        public string IPADDRESS { get; set; } = "";
        /// <summary>
        /// DHCP IP地址
        /// </summary>
        public string IPDHCP { get; set; } = "";
        /// <summary>
        /// 网关IP地址
        /// </summary>
        public string IPGATEWAY { get; set; } = "";
        /// <summary>
        /// 子网掩码
        /// </summary>
        public string IPMASK { get; set; } = "";
        /// <summary>
        /// 子网IP地址
        /// </summary>
        public string IPSUBNET { get; set; } = "";
        /// <summary>
        /// MAC地址
        /// </summary>
        public string MACADDR { get; set; } = "";
        /// <summary>
        /// MTU值
        /// </summary>
        public string MTU { get; set; } = "";
        /// <summary>
        /// 网络速度
        /// </summary>
        public string SPEED { get; set; } = "";
        /// <summary>
        /// 网络状态
        /// </summary>
        public string STATUS { get; set; } = "";
        /// <summary>
        /// 网络类型
        /// </summary>
        public string TYPE { get; set; } = "";
        /// <summary>
        /// MIB类型
        /// </summary>
        public string TYPEMIB { get; set; } = "";
        /// <summary>
        /// 是否为虚拟设备
        /// </summary>
        public int VIRTUALDEV { get; set; }
    }

    /// <summary>
    /// 端口信息类
    /// </summary>
    public class Port
    {
        /// <summary>
        /// 端口标题
        /// </summary>
        public string CAPTION { get; set; } = "";
        /// <summary>
        /// 端口描述
        /// </summary>
        public string DESCRIPTION { get; set; } = "";
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 端口ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 端口名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// 端口类型
        /// </summary>
        public string TYPE { get; set; } = "";
    }

    /// <summary>
    /// 打印机信息类
    /// </summary>
    public class Printer
    {
        /// <summary>
        /// 打印机备注
        /// </summary>
        public string COMMENT { get; set; } = "";
        /// <summary>
        /// 打印机描述
        /// </summary>
        public string DESCRIPTION { get; set; } = "";
        /// <summary>
        /// 打印机驱动
        /// </summary>
        public string DRIVER { get; set; } = "";
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 打印机ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 打印机名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// 是否为网络打印机
        /// </summary>
        public int NETWORK { get; set; }
        /// <summary>
        /// 打印机端口
        /// </summary>
        public string PORT { get; set; } = "";
        /// <summary>
        /// 打印机分辨率
        /// </summary>
        public string RESOLUTION { get; set; } = "";
        /// <summary>
        /// 服务器名称
        /// </summary>
        public string SERVERNAME { get; set; } = "";
        /// <summary>
        /// 是否共享
        /// </summary>
        public int SHARED { get; set; }
        /// <summary>
        /// 共享名称
        /// </summary>
        public string SHARENAME { get; set; } = "";
    }

    /// <summary>
    /// 注册表信息类
    /// </summary>
    public class Registry
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 注册表项ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 注册表项名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// 注册表值
        /// </summary>
        public string REGVALUE { get; set; } = "";
    }

    /// <summary>
    /// 存储库信息类
    /// </summary>
    public class Repository
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 存储库ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 标签
        /// </summary>
        public string TAG { get; set; } = "";
    }

    /// <summary>
    /// SIM卡信息类
    /// </summary>
    public class Sim
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// SIM卡ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 运营商
        /// </summary>
        public string OPERATOR { get; set; } = "";
    }

    /// <summary>
    /// 插槽信息类
    /// </summary>
    public class Slot
    {
        /// <summary>
        /// 插槽描述
        /// </summary>
        public string DESCRIPTION { get; set; } = "";
        /// <summary>
        /// 插槽标识
        /// </summary>
        public string DESIGNATION { get; set; } = "";
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 插槽ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 插槽名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// 是否共享
        /// </summary>
        public int PSHARE { get; set; }
        /// <summary>
        /// 插槽用途
        /// </summary>
        public string PURPOSE { get; set; } = "";
        /// <summary>
        /// 插槽状态
        /// </summary>
        public string STATUS { get; set; } = "";
    }

    /// <summary>
    /// 软件信息类
    /// </summary>
    public class Software
    {
        /// <summary>
        /// 软件架构
        /// </summary>
        public string ARCHITECTURE { get; set; } = "";
        /// <summary>
        /// 位宽
        /// </summary>
        public int BITSWIDTH { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string COMMENTS { get; set; } = "";
        /// <summary>
        /// 文件名
        /// </summary>
        public string FILENAME { get; set; } = "";
        /// <summary>
        /// 文件大小
        /// </summary>
        public int FILESIZE { get; set; }
        /// <summary>
        /// 文件夹
        /// </summary>
        public string FOLDER { get; set; } = "";
        /// <summary>
        /// GUID
        /// </summary>
        public string GUID { get; set; } = "";
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 软件ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 安装日期
        /// </summary>
        public string INSTALLDATE { get; set; } = "";
        /// <summary>
        /// 语言
        /// </summary>
        public string LANGUAGE { get; set; } = "";
        /// <summary>
        /// 名称ID
        /// </summary>
        public int NAME_ID { get; set; }
        /// <summary>
        /// 发布者ID
        /// </summary>
        public int PUBLISHER_ID { get; set; }
        /// <summary>
        /// 来源
        /// </summary>
        public int SOURCE { get; set; }
        /// <summary>
        /// 版本ID
        /// </summary>
        public int VERSION_ID { get; set; }
    }

    /// <summary>
    /// 声音设备信息类
    /// </summary>
    public class Sound
    {
        /// <summary>
        /// 描述
        /// </summary>
        public string DESCRIPTION { get; set; } = "";
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 声音设备ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 制造商
        /// </summary>
        public string MANUFACTURER { get; set; } = "";
        /// <summary>
        /// 名称
        /// </summary>
        public string NAME { get; set; } = "";
    }

    /// <summary>
    /// 存储设备信息类
    /// </summary>
    public class Storage
    {
        /// <summary>
        /// 存储设备描述
        /// </summary>
        public string DESCRIPTION { get; set; } = "";
        /// <summary>
        /// 磁盘大小
        /// </summary>
        public int DISKSIZE { get; set; }
        /// <summary>
        /// 固件版本
        /// </summary>
        public string FIRMWARE { get; set; } = "";
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 存储设备ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 制造商
        /// </summary>
        public string MANUFACTURER { get; set; } = "";
        /// <summary>
        /// 型号
        /// </summary>
        public string MODEL { get; set; } = "";
        /// <summary>
        /// 名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// 序列号
        /// </summary>
        public string SERIALNUMBER { get; set; } = "";
        /// <summary>
        /// 类型
        /// </summary>
        public string TYPE { get; set; } = "";
    }

    /// <summary>
    /// USB设备信息类
    /// </summary>
    public class UsbDevice
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// USB设备ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// 厂商ID
        /// </summary>
        public string VENDORID { get; set; } = "";
        /// <summary>
        /// 产品ID
        /// </summary>
        public string PRODUCTID { get; set; } = "";
    }

    /// <summary>
    /// 视频设备信息类
    /// </summary>
    public class Video
    {
        /// <summary>
        /// 显卡芯片组
        /// </summary>
        public string CHIPSET { get; set; } = "";
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 视频设备ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 显存大小
        /// </summary>
        public string MEMORY { get; set; } = "";
        /// <summary>
        /// 显卡名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// 分辨率
        /// </summary>
        public string RESOLUTION { get; set; } = "";
    }

    /// <summary>
    /// 虚拟机信息类
    /// </summary>
    public class VirtualMachine
    {
        /// <summary>
        /// 硬件ID
        /// </summary>
        public int HARDWARE_ID { get; set; }
        /// <summary>
        /// 虚拟机ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 虚拟机名称
        /// </summary>
        public string NAME { get; set; } = "";
        /// <summary>
        /// 虚拟机类型
        /// </summary>
        public string TYPE { get; set; } = "";
    }
}
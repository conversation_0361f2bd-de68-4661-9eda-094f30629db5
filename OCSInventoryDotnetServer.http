@OCSInventoryDotnetServer_HostAddress = http://localhost:5099

### 获取所有设备ID
GET {{OCSInventoryDotnetServer_HostAddress}}/devices
Accept: application/json

### 获取计算机详情列表（分页）
GET {{OCSInventoryDotnetServer_HostAddress}}/computers?start=0&limit=10
Accept: application/json

### 获取单个计算机详情
GET {{OCSInventoryDotnetServer_HostAddress}}/computer/1
Accept: application/json

### 获取计算机的特定部分（如BIOS信息）
GET {{OCSInventoryDotnetServer_HostAddress}}/computer/1/bios
Accept: application/json

### 搜索计算机
GET {{OCSInventoryDotnetServer_HostAddress}}/computers/search?start=0&limit=10&userid=root
Accept: application/json

### 获取最近更新的计算机
GET {{OCSInventoryDotnetServer_HostAddress}}/computers/lastupdate/86400
Accept: application/json

### 获取软件列表
GET {{OCSInventoryDotnetServer_HostAddress}}/softwares?start=0&limit=10
Accept: application/json

### 搜索特定软件
GET {{OCSInventoryDotnetServer_HostAddress}}/softwares?start=0&limit=10&soft=7-zip
Accept: application/json

### 获取SNMP设备类型列表
GET {{OCSInventoryDotnetServer_HostAddress}}/snmps/typeList
Accept: application/json

### 获取指定类型的SNMP设备
GET {{OCSInventoryDotnetServer_HostAddress}}/snmp/snmp_default?start=0&limit=10
Accept: application/json

### 获取指定SNMP设备详情
GET {{OCSInventoryDotnetServer_HostAddress}}/snmp/snmp_default/1
Accept: application/json

### 获取IP发现网络列表
GET {{OCSInventoryDotnetServer_HostAddress}}/ipdiscover
Accept: application/json

### 获取指定网络的设备列表
GET {{OCSInventoryDotnetServer_HostAddress}}/ipdiscover/network/***********
Accept: application/json

### 根据标签获取设备列表
GET {{OCSInventoryDotnetServer_HostAddress}}/ipdiscover/tag/DEV-MACHINE
Accept: application/json

### 按CVSS评分获取CVE列表
GET {{OCSInventoryDotnetServer_HostAddress}}/cve/cvss?start=0&limit=10
Accept: application/json

### 按软件获取CVE列表
GET {{OCSInventoryDotnetServer_HostAddress}}/cve/software?start=0&limit=10
Accept: application/json

### 按计算机获取CVE列表
GET {{OCSInventoryDotnetServer_HostAddress}}/cve/computer?start=0&limit=10
Accept: application/json

### 获取易受攻击的计算机列表
GET {{OCSInventoryDotnetServer_HostAddress}}/cve/computerslist
Accept: application/json

### 获取CVE历史记录
GET {{OCSInventoryDotnetServer_HostAddress}}/cve/history
Accept: application/json

### 测试错误处理 - 无效的ID
GET {{OCSInventoryDotnetServer_HostAddress}}/computer/-1
Accept: application/json

### 测试错误处理 - 无效的分页参数
GET {{OCSInventoryDotnetServer_HostAddress}}/computers?start=-1&limit=0
Accept: application/json

### 测试错误处理 - 无效的网络ID
GET {{OCSInventoryDotnetServer_HostAddress}}/ipdiscover/network/invalid-network-id
Accept: application/json

###

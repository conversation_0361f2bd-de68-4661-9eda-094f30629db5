{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000725689405"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1377"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"plPWh5ze3m3b/G2Vxe8msRg6EUhLpNKDq98SYv68s7s=\""}, {"Name": "ETag", "Value": "W/\"4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 08:08:37 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3813"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 08:07:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1377"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"plPWh5ze3m3b/G2Vxe8msRg6EUhLpNKDq98SYv68s7s=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 08:08:37 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-plPWh5ze3m3b/G2Vxe8msRg6EUhLpNKDq98SYv68s7s="}]}, {"Route": "index.i40pt9r67p.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000725689405"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1377"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"plPWh5ze3m3b/G2Vxe8msRg6EUhLpNKDq98SYv68s7s=\""}, {"Name": "ETag", "Value": "W/\"4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 08:08:37 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i40pt9r67p"}, {"Name": "integrity", "Value": "sha256-4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.i40pt9r67p.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3813"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 08:07:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i40pt9r67p"}, {"Name": "integrity", "Value": "sha256-4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.i40pt9r67p.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1377"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"plPWh5ze3m3b/G2Vxe8msRg6EUhLpNKDq98SYv68s7s=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 08:08:37 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i40pt9r67p"}, {"Name": "integrity", "Value": "sha256-plPWh5ze3m3b/G2Vxe8msRg6EUhLpNKDq98SYv68s7s="}, {"Name": "label", "Value": "index.html.gz"}]}]}
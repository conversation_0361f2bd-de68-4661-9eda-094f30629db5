{"Version": 1, "Hash": "EY0ft1ydx1j44k7U0hbdQbOyeeRfOT6xr2BnfmU+ImQ=", "Source": "OCSInventoryDotnetServer", "BasePath": "_content/OCSInventoryDotnetServer", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "OCSInventoryDotnetServer\\wwwroot", "Source": "OCSInventoryDotnetServer", "ContentRoot": "D:\\D\\temp\\it-infra\\OCSInventory-Server_.net\\OCSInventoryDotnetServer\\wwwroot\\", "BasePath": "_content/OCSInventoryDotnetServer", "Pattern": "**"}], "Assets": [{"Identity": "D:\\D\\temp\\it-infra\\OCSInventory-Server_.net\\OCSInventoryDotnetServer\\obj\\Debug\\net9.0\\compressed\\rjmyhxfzjz-i40pt9r67p.gz", "SourceId": "OCSInventoryDotnetServer", "SourceType": "Discovered", "ContentRoot": "D:\\D\\temp\\it-infra\\OCSInventory-Server_.net\\OCSInventoryDotnetServer\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OCSInventoryDotnetServer", "RelativePath": "index#[.{fingerprint=i40pt9r67p}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\D\\temp\\it-infra\\OCSInventory-Server_.net\\OCSInventoryDotnetServer\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qwo1ow6a01", "Integrity": "plPWh5ze3m3b/G2Vxe8msRg6EUhLpNKDq98SYv68s7s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\D\\temp\\it-infra\\OCSInventory-Server_.net\\OCSInventoryDotnetServer\\wwwroot\\index.html", "FileLength": 1377, "LastWriteTime": "2025-07-01T08:08:37+00:00"}, {"Identity": "D:\\D\\temp\\it-infra\\OCSInventory-Server_.net\\OCSInventoryDotnetServer\\wwwroot\\index.html", "SourceId": "OCSInventoryDotnetServer", "SourceType": "Discovered", "ContentRoot": "D:\\D\\temp\\it-infra\\OCSInventory-Server_.net\\OCSInventoryDotnetServer\\wwwroot\\", "BasePath": "_content/OCSInventoryDotnetServer", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "i40pt9r67p", "Integrity": "4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 3813, "LastWriteTime": "2025-07-01T08:07:51+00:00"}], "Endpoints": [{"Route": "index.html", "AssetFile": "D:\\D\\temp\\it-infra\\OCSInventory-Server_.net\\OCSInventoryDotnetServer\\obj\\Debug\\net9.0\\compressed\\rjmyhxfzjz-i40pt9r67p.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000725689405"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1377"}, {"Name": "ETag", "Value": "\"plPWh5ze3m3b/G2Vxe8msRg6EUhLpNKDq98SYv68s7s=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 08:08:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw="}]}, {"Route": "index.html", "AssetFile": "D:\\D\\temp\\it-infra\\OCSInventory-Server_.net\\OCSInventoryDotnetServer\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3813"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 08:07:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw="}]}, {"Route": "index.html.gz", "AssetFile": "D:\\D\\temp\\it-infra\\OCSInventory-Server_.net\\OCSInventoryDotnetServer\\obj\\Debug\\net9.0\\compressed\\rjmyhxfzjz-i40pt9r67p.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1377"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"plPWh5ze3m3b/G2Vxe8msRg6EUhLpNKDq98SYv68s7s=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 08:08:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-plPWh5ze3m3b/G2Vxe8msRg6EUhLpNKDq98SYv68s7s="}]}, {"Route": "index.i40pt9r67p.html", "AssetFile": "D:\\D\\temp\\it-infra\\OCSInventory-Server_.net\\OCSInventoryDotnetServer\\obj\\Debug\\net9.0\\compressed\\rjmyhxfzjz-i40pt9r67p.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000725689405"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1377"}, {"Name": "ETag", "Value": "\"plPWh5ze3m3b/G2Vxe8msRg6EUhLpNKDq98SYv68s7s=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 08:08:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i40pt9r67p"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw="}]}, {"Route": "index.i40pt9r67p.html", "AssetFile": "D:\\D\\temp\\it-infra\\OCSInventory-Server_.net\\OCSInventoryDotnetServer\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3813"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 08:07:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i40pt9r67p"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-4v0vUIyOfjhGG/B+riEf59OY5O82BqVkLlYkz/IspHw="}]}, {"Route": "index.i40pt9r67p.html.gz", "AssetFile": "D:\\D\\temp\\it-infra\\OCSInventory-Server_.net\\OCSInventoryDotnetServer\\obj\\Debug\\net9.0\\compressed\\rjmyhxfzjz-i40pt9r67p.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1377"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"plPWh5ze3m3b/G2Vxe8msRg6EUhLpNKDq98SYv68s7s=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 08:08:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i40pt9r67p"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-plPWh5ze3m3b/G2Vxe8msRg6EUhLpNKDq98SYv68s7s="}]}]}
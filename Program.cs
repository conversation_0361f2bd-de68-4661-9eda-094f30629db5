using OCSInventoryDotnetServer.Services;
using OCSInventoryDotnetServer.Extensions;
using OCSInventoryDotnetServer.Middleware;
using Scalar.AspNetCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddOpenApi();
builder.Services.AddScoped<OcsApiService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
app.UseErrorHandling();

if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

//app.UseHttpsRedirection();

// 配置API端点
app.MapComputerEndpoints();
app.MapSoftwareEndpoints();
app.MapSnmpEndpoints();
app.MapIpDiscoverEndpoints();
app.MapCveEndpoints();

//// 添加静态文件支持
app.UseStaticFiles();

app.MapScalarApiReference();

app.Run();



using Microsoft.Extensions.Diagnostics.HealthChecks;
using OCSInventoryDotnetServer.Services;

namespace OCSInventoryDotnetServer.HealthChecks
{
    /// <summary>
    /// OCS API健康检查
    /// </summary>
    public class OcsApiHealthCheck : IHealthCheck
    {
        private readonly OcsApiService _ocsApiService;
        private readonly ILogger<OcsApiHealthCheck> _logger;

        public OcsApiHealthCheck(OcsApiService ocsApiService, ILogger<OcsApiHealthCheck> logger)
        {
            _ocsApiService = ocsApiService;
            _logger = logger;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
        {
                // 尝试获取SNMP类型列表作为健康检查
                // 这是一个轻量级的操作，可以验证OCS API的连通性
                var result = await _ocsApiService.GetSnmpTypesAsync();
                
                if (result != null)
                {
                    _logger.LogInformation("OCS API health check passed");
                    return HealthCheckResult.Healthy("OCS API is responding normally");
                }
                else
                {
                    _logger.LogWarning("OCS API health check returned null result");
                    return HealthCheckResult.Degraded("OCS API returned null result");
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "OCS API health check failed due to HTTP error");
                return HealthCheckResult.Unhealthy("OCS API is not reachable", ex);
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogError(ex, "OCS API health check timed out");
                return HealthCheckResult.Unhealthy("OCS API request timed out", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "OCS API health check failed with unexpected error");
                return HealthCheckResult.Unhealthy("OCS API health check failed", ex);
            }
        }
    }
}
